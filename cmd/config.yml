# server name
rpc_server_name: chaos

# log config
#log_level: trace
log_level: debug
log_write: true
log_dir: ../../logs/chaos
log_json: false

# consul config
# 路由方式: poll=轮训, standby=分组主备, hash=哈希, lb=负载均衡, specify=指定[默认:poll].
rpc_router: poll
msg_router: poll

http_port: 13009
socket_type: ws

# 压测相关配置
st_web_api: 192.168.1.54:21101
st_gateway: 192.168.1.54:21201
st_tcp: 192.168.1.54:31201

# 压测机器人设备码起始位
st_uid_assign_begin: ************
st_player_num: 10000  # 增加到1000个机器人进行压测

consul_addr: 192.168.1.58:8500

watch_log: false

# 游戏配置开关
switch_module:
  hall:
    play_basic: false # Basic
    play_mine: false # Mine
    play_charge: true # 充值
  game:
    play_game: false

# 1s 并发玩家 - 控制登录速度，避免服务器过载
multi_player: 10000

reconnect: false
